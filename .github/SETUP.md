# 🚀 GitHub Actions CI/CD Setup Guide

This guide will help you configure GitHub Actions for your Revisa.AI project with automated CI/CD, security scanning, and deployment to Railway.

## 📋 Prerequisites

- [ ] GitHub repository with admin access
- [ ] Railway account with deployed services
- [ ] Snyk account (free tier available)

## 🔧 Step 1: GitHub Repository Configuration

### 1.1 Enable GitHub Actions
1. Go to your repository on GitHub
2. Navigate to **Settings** → **Actions** → **General**
3. Under "Actions permissions", select **"Allow all actions and reusable workflows"**
4. Under "Workflow permissions", select **"Read and write permissions"**
5. Check **"Allow GitHub Actions to create and approve pull requests"**

### 1.2 Branch Protection Rules
1. Go to **Settings** → **Branches**
2. Click **"Add rule"** for `main` branch
3. Configure:
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - ✅ Require pull request reviews before merging
   - ✅ Include administrators

## 🔐 Step 2: GitHub Secrets Configuration

Go to **Settings** → **Secrets and variables** → **Actions** and add these secrets:

### 2.1 Railway Configuration
```
RAILWAY_TOKEN=your_railway_api_token
```

### 2.2 Production Environment
```
RAILWAY_PROJECT_ID_PROD=your_production_project_id
RAILWAY_SERVICE_SERVER_PROD=your_production_server_service_id
RAILWAY_SERVICE_WWW_PROD=your_production_www_service_id
SERVER_URL_PROD=https://your-server-production.up.railway.app
WWW_URL_PROD=https://your-www-production.up.railway.app
```

### 2.3 Staging Environment
```
RAILWAY_PROJECT_ID_STAGING=your_staging_project_id
RAILWAY_SERVICE_SERVER_STAGING=your_staging_server_service_id
RAILWAY_SERVICE_WWW_STAGING=your_staging_www_service_id
SERVER_URL_STAGING=https://your-server-staging.up.railway.app
WWW_URL_STAGING=https://your-www-staging.up.railway.app
```

### 2.4 Security Scanning
```
SNYK_TOKEN=your_snyk_api_token
```

### 2.5 Database (for manual operations)
```
DATABASE_URL=your_database_connection_string
```

## 🚂 Step 3: Railway Setup

### 3.1 Get Railway API Token
1. Go to [Railway Dashboard](https://railway.app/dashboard)
2. Click on your profile → **Account Settings**
3. Go to **Tokens** tab
4. Click **"Create Token"**
5. Copy the token and add it as `RAILWAY_TOKEN` secret

### 3.2 Get Project and Service IDs
Run these commands in your terminal (install Railway CLI first):

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# List your projects
railway projects

# Connect to your project
railway link

# Get project info
railway status

# List services in the project
railway services
```

Copy the IDs and add them to your GitHub secrets.

### 3.3 Create Staging Environment (Optional)
1. In Railway dashboard, create a new project for staging
2. Deploy your apps to the staging project
3. Get the staging project and service IDs
4. Add them to GitHub secrets

## 🔒 Step 4: Snyk Security Scanning Setup

### 4.1 Create Snyk Account
1. Go to [Snyk.io](https://snyk.io)
2. Sign up for a free account
3. Connect your GitHub repository

### 4.2 Get Snyk API Token
1. Go to **Account Settings** → **API Token**
2. Copy your token
3. Add it as `SNYK_TOKEN` secret in GitHub

## 🌿 Step 5: Branch Strategy Setup

### 5.1 Create Development Branch
```bash
git checkout -b develop
git push -u origin develop
```

### 5.2 Workflow Triggers
- **Pull Requests**: Triggers CI checks (quality, security, build)
- **Push to `develop`**: Deploys to staging environment
- **Push to `main`**: Deploys to production environment
- **Manual**: Use workflow_dispatch for manual operations

## 🧪 Step 6: Test Your Setup

### 6.1 Test CI Pipeline
1. Create a feature branch: `git checkout -b feature/test-ci`
2. Make a small change and commit
3. Push and create a pull request
4. Check that CI workflows run successfully

### 6.2 Test Staging Deployment
1. Merge a change to `develop` branch
2. Check that staging deployment workflow runs
3. Verify your staging environment is updated

### 6.3 Test Production Deployment
1. Merge `develop` to `main`
2. Check that production deployment workflow runs
3. Verify your production environment is updated

## 🛠️ Step 7: Manual Operations

Use the manual operations workflow for:
- Database migrations: `database-migrate`
- Database seeding: `database-seed`
- Cache clearing: `cache-clear`
- Health checks: `health-check`
- Rollbacks: `rollback`

To trigger:
1. Go to **Actions** tab in GitHub
2. Select **"Manual Operations"** workflow
3. Click **"Run workflow"**
4. Choose operation and environment
5. Type "confirm" to proceed

## 📊 Step 8: Monitoring Your Workflows

### 8.1 Workflow Status
- Check the **Actions** tab for workflow runs
- Set up notifications for failed workflows
- Review workflow summaries and artifacts

### 8.2 Security Alerts
- Monitor Dependabot alerts
- Review Snyk security reports
- Check CodeQL analysis results

## 🚨 Troubleshooting

### Common Issues

**Railway CLI Authentication**
```bash
railway logout
railway login
```

**Missing Secrets**
- Double-check all required secrets are set
- Verify secret names match exactly
- Check environment-specific secrets

**Build Failures**
- Check Node.js version compatibility
- Verify all dependencies are in package.json
- Review build logs for specific errors

**Deployment Failures**
- Verify Railway service IDs are correct
- Check Railway project permissions
- Review Railway deployment logs

## 📚 Next Steps

1. **Set up monitoring**: Add application monitoring and alerting
2. **Add more tests**: Expand test coverage as your app grows
3. **Performance monitoring**: Add performance tracking and optimization
4. **Security hardening**: Implement additional security measures

## 🆘 Need Help?

- Check GitHub Actions documentation
- Review Railway deployment guides
- Check workflow run logs for detailed error messages
- Create an issue in your repository for team discussion
