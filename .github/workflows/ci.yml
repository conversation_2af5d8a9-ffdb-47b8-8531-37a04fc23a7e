name: 🔍 CI - Code Quality & Security

on:
  pull_request:
    branches: [main, develop]
  push:
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # =====================================================
  # Code Quality & Type Safety
  # =====================================================
  quality:
    name: 📝 Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Type checking
        run: npm run typecheck

      - name: 🧹 Linting
        run: npm run lint

      - name: 💅 Format checking
        run: npm run format:check

  # =====================================================
  # Security Scanning
  # =====================================================
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: 🔍 CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # =====================================================
  # Build Verification
  # =====================================================
  build:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      matrix:
        app: [server, www]

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏗️ Build ${{ matrix.app }}
        run: cd apps/${{ matrix.app }} && npm run build

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.app }}
          path: apps/${{ matrix.app }}/dist
          retention-days: 1

  # =====================================================
  # Basic Tests (if they exist)
  # =====================================================
  test:
    name: 🧪 Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: github.event_name == 'pull_request'

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run tests
        run: npm run test
        continue-on-error: true

  # =====================================================
  # Summary
  # =====================================================
  ci-success:
    name: ✅ CI Success
    runs-on: ubuntu-latest
    needs: [quality, security, build]
    if: always()

    steps:
      - name: ✅ All checks passed
        if: ${{ needs.quality.result == 'success' && needs.security.result == 'success' && needs.build.result == 'success' }}
        run: echo "🎉 All CI checks passed!"

      - name: ❌ Some checks failed
        if: ${{ needs.quality.result != 'success' || needs.security.result != 'success' || needs.build.result != 'success' }}
        run: |
          echo "❌ Some CI checks failed:"
          echo "Quality: ${{ needs.quality.result }}"
          echo "Security: ${{ needs.security.result }}"
          echo "Build: ${{ needs.build.result }}"
          exit 1
