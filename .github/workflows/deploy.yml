name: 🚀 Deploy to Railway

on:
  # Disabled: Railway auto-deploy handles this
  # push:
  #   branches:
  #     - main        # Production deployment
  #     - develop     # Staging deployment
  workflow_dispatch:  # Manual deployment only
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

concurrency:
  group: deploy-${{ github.ref }}
  cancel-in-progress: false  # Don't cancel deployments

jobs:
  # =====================================================
  # Determine Environment
  # =====================================================
  setup:
    name: 🎯 Setup Deployment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      railway-service-server: ${{ steps.env.outputs.railway-service-server }}
      railway-service-www: ${{ steps.env.outputs.railway-service-www }}
      railway-project-id: ${{ steps.env.outputs.railway-project-id }}

    steps:
      - name: 🎯 Determine environment
        id: env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENV="${{ github.event.inputs.environment }}"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            ENV="production"
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            ENV="staging"
          else
            ENV="staging"
          fi
          
          echo "environment=$ENV" >> $GITHUB_OUTPUT
          echo "🎯 Deploying to: $ENV"
          
          # Set Railway service IDs based on environment
          if [[ "$ENV" == "production" ]]; then
            echo "railway-service-server=${{ secrets.RAILWAY_SERVICE_SERVER_PROD }}" >> $GITHUB_OUTPUT
            echo "railway-service-www=${{ secrets.RAILWAY_SERVICE_WWW_PROD }}" >> $GITHUB_OUTPUT
            echo "railway-project-id=${{ secrets.RAILWAY_PROJECT_ID_PROD }}" >> $GITHUB_OUTPUT
          else
            echo "railway-service-server=${{ secrets.RAILWAY_SERVICE_SERVER_STAGING }}" >> $GITHUB_OUTPUT
            echo "railway-service-www=${{ secrets.RAILWAY_SERVICE_WWW_STAGING }}" >> $GITHUB_OUTPUT
            echo "railway-project-id=${{ secrets.RAILWAY_PROJECT_ID_STAGING }}" >> $GITHUB_OUTPUT
          fi

  # =====================================================
  # Deploy Server
  # =====================================================
  deploy-server:
    name: 🖥️ Deploy Server
    runs-on: ubuntu-latest
    needs: setup
    environment: ${{ needs.setup.outputs.environment }}
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: 🚂 Install Railway CLI
        run: npm install -g @railway/cli

      - name: 🚀 Deploy Server to Railway
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        run: |
          echo "🚀 Deploying server to ${{ needs.setup.outputs.environment }}..."
          railway up --service ${{ needs.setup.outputs.railway-service-server }}

      - name: ⏳ Wait for deployment
        run: |
          echo "⏳ Waiting for deployment to complete..."
          sleep 30

  # =====================================================
  # Deploy Frontend
  # =====================================================
  deploy-www:
    name: 🌐 Deploy Frontend
    runs-on: ubuntu-latest
    needs: [setup, deploy-server]
    environment: ${{ needs.setup.outputs.environment }}
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: 🚂 Install Railway CLI
        run: npm install -g @railway/cli

      - name: 🚀 Deploy Frontend to Railway
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        run: |
          echo "🚀 Deploying frontend to ${{ needs.setup.outputs.environment }}..."
          railway up --service ${{ needs.setup.outputs.railway-service-www }}

      - name: ⏳ Wait for deployment
        run: |
          echo "⏳ Waiting for deployment to complete..."
          sleep 30

  # =====================================================
  # Health Check
  # =====================================================
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [setup, deploy-server, deploy-www]
    timeout-minutes: 5

    steps:
      - name: 🏥 Check server health
        run: |
          if [[ "${{ needs.setup.outputs.environment }}" == "production" ]]; then
            SERVER_URL="${{ secrets.SERVER_URL_PROD }}"
            WWW_URL="${{ secrets.WWW_URL_PROD }}"
          else
            SERVER_URL="${{ secrets.SERVER_URL_STAGING }}"
            WWW_URL="${{ secrets.WWW_URL_STAGING }}"
          fi
          
          echo "🏥 Checking server health: $SERVER_URL"
          curl -f "$SERVER_URL" || exit 1
          
          echo "🏥 Checking frontend health: $WWW_URL"
          curl -f "$WWW_URL" || exit 1
          
          echo "✅ All services are healthy!"

  # =====================================================
  # Deployment Summary
  # =====================================================
  deployment-summary:
    name: 📋 Deployment Summary
    runs-on: ubuntu-latest
    needs: [setup, deploy-server, deploy-www, health-check]
    if: always()

    steps:
      - name: 📋 Deployment Summary
        run: |
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: ${{ needs.setup.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Server**: ${{ needs.deploy-server.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Frontend**: ${{ needs.deploy-www.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Health Check**: ${{ needs.health-check.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
