name: 🛠️ Manual Operations

on:
  workflow_dispatch:
    inputs:
      operation:
        description: 'Operation to perform'
        required: true
        type: choice
        options:
          - database-migrate
          - database-seed
          - cache-clear
          - health-check
          - rollback
      environment:
        description: 'Environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      confirm:
        description: 'Type "confirm" to proceed'
        required: true
        type: string

jobs:
  # =====================================================
  # Validation
  # =====================================================
  validate:
    name: ✅ Validate Operation
    runs-on: ubuntu-latest
    
    steps:
      - name: ✅ Validate confirmation
        if: github.event.inputs.confirm != 'confirm'
        run: |
          echo "❌ Operation not confirmed. Please type 'confirm' to proceed."
          exit 1
      
      - name: ⚠️ Production warning
        if: github.event.inputs.environment == 'production'
        run: |
          echo "⚠️ WARNING: This operation will affect PRODUCTION environment!"
          echo "Operation: ${{ github.event.inputs.operation }}"
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "Triggered by: ${{ github.actor }}"

  # =====================================================
  # Database Operations
  # =====================================================
  database-operations:
    name: 🗄️ Database Operations
    runs-on: ubuntu-latest
    needs: validate
    if: contains(fromJson('["database-migrate", "database-seed"]'), github.event.inputs.operation)
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Database Migration
        if: github.event.inputs.operation == 'database-migrate'
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: |
          echo "🗄️ Running database migrations..."
          npx prisma migrate deploy
          echo "✅ Database migrations completed"

      - name: 🌱 Database Seed
        if: github.event.inputs.operation == 'database-seed'
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: |
          echo "🌱 Seeding database..."
          npx prisma db seed
          echo "✅ Database seeding completed"

  # =====================================================
  # Cache Operations
  # =====================================================
  cache-operations:
    name: 🗂️ Cache Operations
    runs-on: ubuntu-latest
    needs: validate
    if: github.event.inputs.operation == 'cache-clear'
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: 🗂️ Clear Cache
        run: |
          echo "🗂️ Clearing application cache..."
          # Add your cache clearing logic here
          # For example, Redis FLUSHALL command
          echo "✅ Cache cleared successfully"

  # =====================================================
  # Health Check
  # =====================================================
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: validate
    if: github.event.inputs.operation == 'health-check'

    steps:
      - name: 🏥 Comprehensive Health Check
        run: |
          if [[ "${{ github.event.inputs.environment }}" == "production" ]]; then
            SERVER_URL="${{ secrets.SERVER_URL_PROD }}"
            WWW_URL="${{ secrets.WWW_URL_PROD }}"
          else
            SERVER_URL="${{ secrets.SERVER_URL_STAGING }}"
            WWW_URL="${{ secrets.WWW_URL_STAGING }}"
          fi
          
          echo "🏥 Checking server health: $SERVER_URL"
          curl -f -v "$SERVER_URL" || exit 1
          
          echo "🏥 Checking frontend health: $WWW_URL"
          curl -f -v "$WWW_URL" || exit 1
          
          echo "✅ All services are healthy!"

  # =====================================================
  # Rollback Operations
  # =====================================================
  rollback:
    name: ⏪ Rollback
    runs-on: ubuntu-latest
    needs: validate
    if: github.event.inputs.operation == 'rollback'
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: ⏪ Rollback Deployment
        run: |
          echo "⏪ Rolling back deployment..."
          echo "This would trigger a rollback to the previous deployment"
          echo "Environment: ${{ github.event.inputs.environment }}"
          # Add your rollback logic here
          echo "✅ Rollback completed"

  # =====================================================
  # Operation Summary
  # =====================================================
  summary:
    name: 📋 Operation Summary
    runs-on: ubuntu-latest
    needs: [validate, database-operations, cache-operations, health-check, rollback]
    if: always()

    steps:
      - name: 📋 Create Summary
        run: |
          echo "## 🛠️ Manual Operation Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Operation**: ${{ github.event.inputs.operation }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: ${{ github.event.inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Time**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
