# 🚀 Revisa.AI

A production-grade ChatGPT-style application built with modern technologies and deploy-agnostic architecture.

## 🏗️ Architecture

**Monorepo Structure:**

- **NestJS Server** - Backend API with authentication, chat management, and LLM integration
- **React Router Client** - Modern frontend with real-time chat interface
- **Shared Packages** - Common configurations and utilities
- **PostgreSQL** - Primary database for users, chats, and messages
- **Redis** - Caching and job queue management
- **Prisma ORM** - Type-safe database access and migrations

## ⚡ Quick Start

### Prerequisites

- **Node.js 18+**
- **npm 10+**
- **Docker & Docker Compose v2**
- **Git**

### 🎯 One-Command Setup

```bash
# Clone and setup everything
git clone <your-repo>
cd revisa.ai
npm run setup:dev
```

This will:

- ✅ Install all dependencies
- ✅ Start PostgreSQL and Redis containers
- ✅ Create development environment file
- ✅ Display next steps

### 📊 Database Setup

```bash
# Set up database schema
npm run db:migrate

# Add sample data
npm run db:seed

# Open Prisma Studio (optional)
npm run db:studio
```

### 🔥 Start Development

```bash
# Start all services
npm run dev

# Or start individually
npm run dev:server    # NestJS on :3001
npm run dev:www       # React on :3000
```

## 🛠️ Development Commands

### Database Management

```bash
npm run db:up          # Start PostgreSQL & Redis containers
npm run db:down        # Stop database containers
npm run db:migrate     # Run database migrations
npm run db:reset       # Reset database (dev only)
npm run db:seed        # Seed with sample data
npm run db:studio      # Open Prisma Studio
npm run db:logs        # View database logs
```

### Application Development

```bash
npm run dev            # Start all services
npm run dev:server     # Start NestJS server only
npm run dev:www        # Start React client only
npm run build          # Build all applications
npm run test           # Run all tests
npm run lint           # Lint all code
npm run format         # Format all code
```

### Docker Development

```bash
npm run docker:dev     # Full containerized development
npm run docker:dev:build # Rebuild and start containers
```

## 🌍 Environment Management

### Development Environment

```bash
# Copy environment template
cp .env.example .env.development

# Edit with your values
# - Database URLs (automatically set for local development)
# - API keys (OpenAI, OpenRouter, Azure)
# - Authentication secrets
```

### Production Environment

The application is **deploy-agnostic** and works with:

- ✅ **Railway** (current deployment)
- ✅ **AWS** (ECS, RDS, ElastiCache)
- ✅ **Google Cloud** (Cloud Run, Cloud SQL, Memorystore)
- ✅ **Azure** (Container Instances, PostgreSQL, Redis Cache)
- ✅ **Self-hosted** (Docker Compose)

## 🚀 Production Deployment

### Current: Railway

**Live URLs:**

- **Server**: https://revisa-server-production.up.railway.app
- **Client**: https://revisa-www-production.up.railway.app

**Deployment Process:**

1. Push to main branch
2. Railway auto-deploys both services
3. Database migrations run automatically

### Alternative: Docker Compose

```bash
# Production deployment
npm run docker:prod:build
```

## 📊 Database Schema

**Core Entities:**

- **Users** - Authentication and user management
- **Threads** - Chat conversations
- **Messages** - Individual chat messages
- **ApiUsage** - LLM API usage tracking
- **Sessions** - User session management

**Sample Accounts (Development):**

- Admin: `<EMAIL>` / `admin123`
- Test: `<EMAIL>` / `test123`

```

## 🔧 Technology Stack

### Backend
- **NestJS** - Node.js framework
- **Prisma** - Database ORM
- **PostgreSQL** - Primary database
- **Redis** - Caching and queues
- **JWT** - Authentication
- **OpenAI API** - LLM integration

### Frontend
- **React Router** - Modern React framework
- **TypeScript** - Type safety
- **Vite** - Build tool
- **TailwindCSS** - Styling
- **Server-Sent Events** - Real-time streaming

### DevOps
- **TurboRepo** - Monorepo management
- **Docker** - Containerization
- **Railway** - Cloud deployment
- **GitHub Actions** - CI/CD (planned)

## 📁 Project Structure

```

revisa-ai/
├── 📱 apps/
│ ├── server/ # NestJS Backend
│ │ ├── src/
│ │ │ ├── app.controller.ts
│ │ │ ├── app.service.ts
│ │ │ └── main.ts
│ │ ├── Dockerfile
│ │ └── railway.json
│ └── www/ # React Router Frontend
│ ├── app/
│ │ ├── routes/
│ │ └── welcome/
│ ├── Dockerfile
│ └── railway.json
├── 📦 packages/
│ ├── eslint-config/ # Shared ESLint config
│ └── typescript-config/ # Shared TypeScript config
├── 🗄️ prisma/
│ ├── schema.prisma # Database schema
│ └── seed.ts # Sample data
├── 🐳 docker-compose.dev.yml # Development containers
├── 📜 scripts/
│ └── dev-setup.sh # Development setup
├── 🔧 turbo.json # TurboRepo configuration
└── 📋 package.json # Root package configuration

````

## 🔐 Security Features

- ✅ **JWT Authentication** with secure sessions
- ✅ **Password Hashing** with bcrypt
- ✅ **CORS Protection** for cross-origin requests
- ✅ **Rate Limiting** for API protection
- ✅ **Input Validation** and sanitization
- ✅ **Environment Secrets** management

## 🚦 Development Workflow

1. **Setup**: `npm run setup:dev`
2. **Database**: `npm run db:migrate && npm run db:seed`
3. **Development**: `npm run dev`
4. **Testing**: `npm run test`
5. **Production**: Deploy to Railway

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `npm run test`
5. Run linting: `npm run lint`
6. Commit changes: `git commit -m 'Add amazing feature'`
7. Push to branch: `git push origin feature/amazing-feature`
8. Submit a pull request

## 🆘 Troubleshooting

### Common Issues

**Development server won't start:**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
````

**Database connection issues:**

```bash
# Restart database containers
npm run db:down
npm run db:up
```

**Docker build fails:**

```bash
# Clean Docker cache
docker system prune -f
```

**Port conflicts:**

```bash
# Check what's using ports
lsof -i :3000
lsof -i :3001
lsof -i :5432
lsof -i :6379
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using modern web technologies**
