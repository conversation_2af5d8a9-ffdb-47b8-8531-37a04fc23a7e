import { <PERSON>, Get, Param, <PERSON><PERSON>, Sse } from '@nestjs/common';
import { Response } from 'express';
import { Observable, interval, map, take } from 'rxjs';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('api/test')
  getTestData() {
    return {
      message: 'Hello from Railway server!',
      timestamp: new Date().toISOString(),
      server: 'NestJS on Railway',
      status: 'success',
    };
  }

  @Get('api/user/:id')
  getUserData(@Param('id') id: string, @Res() res: Response) {
    const userData = {
      id: id || Math.floor(Math.random() * 1000),
      name: 'Test User',
      email: '<EMAIL>',
      createdAt: new Date().toISOString(),
      preferences: {
        theme: 'dark',
        notifications: true,
      },
    };

    // Simulate some processing time
    setTimeout(() => {
      res.json(userData);
    }, 500);
  }

  @Sse('api/stream/typing')
  streamTypingText(): Observable<any> {
    const text =
      'Hello! This is a streaming text animation from the Railway server. Each character appears one by one, simulating a typing effect. This demonstrates Server-Sent Events (SSE) working between your React client and NestJS server deployed on Railway! 🚀';

    return interval(10).pipe(
      take(text.length),
      map((index) => ({
        data: {
          char: text[index],
          index,
          isComplete: index === text.length - 1,
          timestamp: new Date().toISOString(),
        },
      })),
    );
  }

  @Sse('api/stream/progress')
  streamProgress(): Observable<any> {
    return interval(20).pipe(
      take(101),
      map((value) => ({
        data: {
          progress: value,
          message: `Processing... ${value}%`,
          isComplete: value === 100,
          timestamp: new Date().toISOString(),
        },
      })),
    );
  }
}
