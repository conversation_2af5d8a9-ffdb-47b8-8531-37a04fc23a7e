import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  console.log('🔄 Bootstrap function called');
  console.log('📍 Current working directory:', process.cwd());
  console.log('🌍 Environment variables:', {
    NODE_ENV: process.env.NODE_ENV,
    PORT: process.env.PORT,
    DATABASE_URL: process.env.DATABASE_URL ? 'SET' : 'NOT SET'
  });

  try {
    console.log('🏗️ Creating NestJS application...');
    const app = await NestFactory.create(AppModule);

    // Enable CORS for Railway deployment
    console.log('🔧 Configuring CORS...');
    app.enableCors({
      origin: [
        'http://localhost:5173', // Development frontend
        'http://localhost:3000', // Alternative dev port
        'https://revisa-www-production.up.railway.app', // Railway frontend
      ],
      credentials: true,
    });

    const port = process.env.PORT ?? 3000;
    console.log(`🚀 Starting server on port ${port}...`);
    await app.listen(port);
    console.log(`✅ Server successfully running on port ${port}`);
    console.log(`🌐 Health check endpoint: http://localhost:${port}/`);
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

console.log('📦 Main.ts file loaded, calling bootstrap...');
bootstrap().catch((error) => {
  console.error('💥 Bootstrap failed:', error);
  process.exit(1);
});
