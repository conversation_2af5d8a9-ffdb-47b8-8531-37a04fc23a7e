#!/bin/sh

echo "🚀 Railway Startup Script"
echo "========================"

# Set error handling
set -e

echo "📍 Current directory: $(pwd)"
echo "📁 Files in current directory:"
ls -la

echo "🗄️ Running database migrations..."
npx prisma migrate deploy --schema=./prisma/schema.prisma

echo "✅ Migrations completed successfully"

echo "📦 Starting Node.js server..."
echo "🌍 Environment: NODE_ENV=$NODE_ENV, PORT=$PORT"

exec node dist/main.js
