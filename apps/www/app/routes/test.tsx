import { useState, useEffect } from "react";

const SERVER_URL = "https://revisa-server-production.up.railway.app";

export default function TestPage() {
  const [testData, setTestData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [typingText, setTypingText] = useState("");
  const [progress, setProgress] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [isProgressComplete, setIsProgressComplete] = useState(false);

  // Test basic HTTP request
  const fetchTestData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${SERVER_URL}/api/test`);
      const data = await response.json();
      setTestData(data);
    } catch (error) {
      console.error("Error fetching test data:", error);
      setTestData({ error: "Failed to fetch data" });
    } finally {
      setLoading(false);
    }
  };

  // Test parameterized HTTP request
  const fetchUserData = async () => {
    setLoading(true);
    try {
      const userId = Math.floor(Math.random() * 1000);
      const response = await fetch(`${SERVER_URL}/api/user/${userId}`);
      const data = await response.json();
      setUserData(data);
    } catch (error) {
      console.error("Error fetching user data:", error);
      setUserData({ error: "Failed to fetch user data" });
    } finally {
      setLoading(false);
    }
  };

  // Test SSE streaming for typing animation
  const startTypingStream = () => {
    setTypingText("");
    setIsTypingComplete(false);

    const eventSource = new EventSource(`${SERVER_URL}/api/stream/typing`);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setTypingText((prev) => prev + data.char);

      if (data.isComplete) {
        setIsTypingComplete(true);
        eventSource.close();
      }
    };

    eventSource.onerror = (error) => {
      console.error("SSE Error:", error);
      eventSource.close();
    };
  };

  // Test SSE streaming for progress
  const startProgressStream = () => {
    setProgress(0);
    setIsProgressComplete(false);

    const eventSource = new EventSource(`${SERVER_URL}/api/stream/progress`);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setProgress(data.progress);

      if (data.isComplete) {
        setIsProgressComplete(true);
        eventSource.close();
      }
    };

    eventSource.onerror = (error) => {
      console.error("SSE Error:", error);
      eventSource.close();
    };
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Railway Connection Test
        </h1>

        <div className="grid gap-6">
          {/* HTTP Requests Section */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              HTTP Requests
            </h2>

            <div className="space-y-4">
              <div>
                <button
                  onClick={fetchTestData}
                  disabled={loading}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-md transition-colors"
                >
                  {loading ? "Loading..." : "Fetch Test Data"}
                </button>

                {testData && (
                  <div className="mt-3 p-3 bg-gray-100 rounded-md">
                    <pre className="text-sm text-gray-700">
                      {JSON.stringify(testData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>

              <div>
                <button
                  onClick={fetchUserData}
                  disabled={loading}
                  className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-2 rounded-md transition-colors"
                >
                  {loading ? "Loading..." : "Fetch User Data"}
                </button>

                {userData && (
                  <div className="mt-3 p-3 bg-gray-100 rounded-md">
                    <pre className="text-sm text-gray-700">
                      {JSON.stringify(userData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* SSE Streaming Section */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Server-Sent Events (SSE) Streaming
            </h2>

            <div className="space-y-6">
              {/* Typing Animation */}
              <div>
                <button
                  onClick={startTypingStream}
                  className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md transition-colors"
                >
                  Start Typing Animation
                </button>

                <div className="mt-3 p-4 bg-gray-100 rounded-md min-h-[100px]">
                  <p className="text-gray-800 leading-relaxed">
                    {typingText}
                    {!isTypingComplete && typingText && (
                      <span className="animate-pulse">|</span>
                    )}
                  </p>
                  {isTypingComplete && (
                    <p className="text-green-600 text-sm mt-2">
                      ✓ Typing complete!
                    </p>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              <div>
                <button
                  onClick={startProgressStream}
                  className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors"
                >
                  Start Progress Stream
                </button>

                <div className="mt-3">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-orange-500 h-2 rounded-full transition-all duration-200"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  {isProgressComplete && (
                    <p className="text-green-600 text-sm mt-2">
                      ✓ Progress complete!
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Connection Info */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Connection Info
            </h2>
            <div className="space-y-2 text-sm text-gray-600">
              <p>
                <strong>Server URL:</strong> {SERVER_URL}
              </p>
              <p>
                <strong>Client URL:</strong>{" "}
                https://revisa-www-production.up.railway.app
              </p>
              <p>
                <strong>Status:</strong>{" "}
                <span className="text-green-600">Connected via Railway</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
