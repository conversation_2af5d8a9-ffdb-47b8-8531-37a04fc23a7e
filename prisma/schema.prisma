// =============================================================================
// REVISA.AI PRISMA SCHEMA
// =============================================================================
// This schema defines the database structure for the ChatGPT-style application
// It's designed to be deploy-agnostic and work across different environments

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =============================================================================
// USER MANAGEMENT
// =============================================================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String   // Hashed password
  tier      UserTier @default(FREE)
  apiQuota  Int      @default(100000) @map("api_quota")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  threads   Thread[]
  apiUsage  ApiUsage[]
  sessions  Session[]

  @@map("users")
}

enum UserTier {
  FREE
  PRO
  ENTERPRISE

  @@map("user_tier")
}

// =============================================================================
// AUTHENTICATION & SESSIONS
// =============================================================================

model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// =============================================================================
// CHAT SYSTEM
// =============================================================================

model Thread {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  title     String
  model     String   @default("gpt-4o-mini")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@index([userId])
  @@index([createdAt(sort: Desc)])
  @@map("threads")
}

model Message {
  id        String      @id @default(cuid())
  threadId  String      @map("thread_id")
  role      MessageRole
  content   String
  tokens    Int         @default(0)
  createdAt DateTime    @default(now()) @map("created_at")

  // Relations
  thread Thread @relation(fields: [threadId], references: [id], onDelete: Cascade)

  @@index([threadId])
  @@index([createdAt])
  @@map("messages")
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM

  @@map("message_role")
}

// =============================================================================
// API USAGE & BILLING
// =============================================================================

model ApiUsage {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  provider  String   // openai, openrouter, azure, etc.
  model     String   // gpt-4o, claude-3, etc.
  tokens    Int
  cost      Decimal  @default(0) @db.Decimal(10, 6)
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
  @@map("api_usage")
}

// =============================================================================
// SYSTEM CONFIGURATION
// =============================================================================

model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String

  @@map("system_config")
}
