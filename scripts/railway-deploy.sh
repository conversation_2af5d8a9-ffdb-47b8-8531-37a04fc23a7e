#!/bin/bash

# =============================================================================
# RAILWAY DEPLOYMENT SCRIPT
# =============================================================================
# This script runs database migrations on Railway deployment
# It should be called as a Railway deployment hook

set -e

echo "🚀 Railway Deployment Script Starting..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[RAILWAY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in Railway environment
if [ -z "$RAILWAY_ENVIRONMENT" ]; then
    print_error "Not running in Railway environment. Skipping deployment script."
    exit 0
fi

print_status "Running in Railway environment: $RAILWAY_ENVIRONMENT"

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL not set. Cannot run migrations."
    exit 1
fi

print_status "Database URL configured"

# Run database migrations
print_status "Running database migrations..."
npx prisma migrate deploy

print_success "Database migrations completed"

# Generate Prisma client (should already be done in Dockerfile, but just in case)
print_status "Ensuring Prisma client is generated..."
npx prisma generate

print_success "Prisma client ready"

print_success "Railway deployment script completed successfully! 🎉"
