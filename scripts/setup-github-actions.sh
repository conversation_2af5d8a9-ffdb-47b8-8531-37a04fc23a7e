#!/bin/bash

# 🚀 GitHub Actions Setup Helper Script
# This script helps you gather the necessary information for GitHub Actions setup

set -e

echo "🚀 GitHub Actions Setup Helper"
echo "=============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo -e "${RED}❌ Railway CLI is not installed${NC}"
    echo "Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

echo -e "${GREEN}✅ Railway CLI found${NC}"
echo ""

# Check if user is logged in to Railway
if ! railway whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️ Not logged in to Railway${NC}"
    echo "Please login first:"
    echo "railway login"
    exit 1
fi

echo -e "${GREEN}✅ Logged in to Railway as: $(railway whoami)${NC}"
echo ""

# Function to get Railway project info
get_railway_info() {
    local env_name=$1
    echo -e "${BLUE}📋 Getting Railway information for ${env_name}...${NC}"
    
    # Check if we're in a Railway project
    if ! railway status &> /dev/null; then
        echo -e "${YELLOW}⚠️ Not linked to a Railway project${NC}"
        echo "Please link to your ${env_name} project:"
        echo "railway link"
        return 1
    fi
    
    # Get project info
    PROJECT_ID=$(railway status --json | jq -r '.project.id')
    PROJECT_NAME=$(railway status --json | jq -r '.project.name')
    
    echo "Project: ${PROJECT_NAME}"
    echo "Project ID: ${PROJECT_ID}"
    
    # Get services
    echo ""
    echo "Services in this project:"
    railway services --json | jq -r '.[] | "- \(.name): \(.id)"'
    
    echo ""
    echo -e "${GREEN}Copy these values to your GitHub secrets:${NC}"
    echo "RAILWAY_PROJECT_ID_${env_name^^}=${PROJECT_ID}"
    
    # Get service IDs
    SERVER_SERVICE_ID=$(railway services --json | jq -r '.[] | select(.name | contains("server")) | .id' | head -1)
    WWW_SERVICE_ID=$(railway services --json | jq -r '.[] | select(.name | contains("www") or contains("web") or contains("client")) | .id' | head -1)
    
    if [ ! -z "$SERVER_SERVICE_ID" ]; then
        echo "RAILWAY_SERVICE_SERVER_${env_name^^}=${SERVER_SERVICE_ID}"
    fi
    
    if [ ! -z "$WWW_SERVICE_ID" ]; then
        echo "RAILWAY_SERVICE_WWW_${env_name^^}=${WWW_SERVICE_ID}"
    fi
    
    echo ""
}

# Function to get Railway URLs
get_railway_urls() {
    local env_name=$1
    echo -e "${BLUE}🌐 Getting deployment URLs for ${env_name}...${NC}"
    
    # Get deployments
    railway deployments --json | jq -r '.[] | select(.status == "SUCCESS") | "\(.service.name): \(.url)"' | head -2
    
    echo ""
    echo -e "${GREEN}Add these URLs to your GitHub secrets:${NC}"
    echo "SERVER_URL_${env_name^^}=https://your-server-${env_name}.up.railway.app"
    echo "WWW_URL_${env_name^^}=https://your-www-${env_name}.up.railway.app"
    echo ""
}

# Main setup process
echo -e "${BLUE}🔧 Setting up GitHub Actions for Railway deployment${NC}"
echo ""

# Get Railway API token instructions
echo -e "${YELLOW}📝 Step 1: Get Railway API Token${NC}"
echo "1. Go to https://railway.app/account/tokens"
echo "2. Create a new token"
echo "3. Copy the token"
echo "4. Add it to GitHub secrets as: RAILWAY_TOKEN"
echo ""

# Production environment
echo -e "${YELLOW}📝 Step 2: Production Environment${NC}"
get_railway_info "production"
get_railway_urls "production"

# Ask about staging environment
echo -e "${YELLOW}📝 Step 3: Staging Environment (Optional)${NC}"
read -p "Do you have a separate staging environment? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Please link to your staging project and run this script again, or manually get the staging project info."
    echo ""
fi

# Snyk setup
echo -e "${YELLOW}📝 Step 4: Snyk Security Scanning${NC}"
echo "1. Go to https://snyk.io and create a free account"
echo "2. Go to Account Settings → API Token"
echo "3. Copy your token"
echo "4. Add it to GitHub secrets as: SNYK_TOKEN"
echo ""

# Database URL
echo -e "${YELLOW}📝 Step 5: Database URL${NC}"
echo "Get your database connection string from Railway:"
echo "1. Go to your Railway project"
echo "2. Click on your database service"
echo "3. Go to Variables tab"
echo "4. Copy the DATABASE_URL or connection string"
echo "5. Add it to GitHub secrets as: DATABASE_URL"
echo ""

# GitHub setup instructions
echo -e "${YELLOW}📝 Step 6: GitHub Repository Setup${NC}"
echo "1. Go to your GitHub repository"
echo "2. Navigate to Settings → Actions → General"
echo "3. Set 'Workflow permissions' to 'Read and write permissions'"
echo "4. Check 'Allow GitHub Actions to create and approve pull requests'"
echo "5. Go to Settings → Secrets and variables → Actions"
echo "6. Add all the secrets mentioned above"
echo ""

# Branch protection
echo -e "${YELLOW}📝 Step 7: Branch Protection (Recommended)${NC}"
echo "1. Go to Settings → Branches"
echo "2. Add protection rule for 'main' branch"
echo "3. Enable 'Require status checks to pass before merging'"
echo "4. Enable 'Require branches to be up to date before merging'"
echo ""

# Final instructions
echo -e "${GREEN}🎉 Setup Complete!${NC}"
echo ""
echo "Next steps:"
echo "1. Add all the secrets to your GitHub repository"
echo "2. Create a 'develop' branch: git checkout -b develop && git push -u origin develop"
echo "3. Create a test pull request to verify CI is working"
echo "4. Check the .github/SETUP.md file for detailed instructions"
echo ""
echo -e "${BLUE}📚 For detailed setup instructions, see: .github/SETUP.md${NC}"
